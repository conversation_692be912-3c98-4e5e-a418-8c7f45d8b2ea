# Validation Test cho FormProducts

## Các trường bắt buộc đã được thêm validation:

### 1. T<PERSON><PERSON> sản phẩm (name)
- **Trường**: `name`
- **Validation**: <PERSON><PERSON><PERSON> buộc nhập
- **Thông báo lỗi**: "Tên sản phẩm là trường bắt buộc"
- **Vị trí**: Dòng 251-256 trong FormProducts/index.tsx

### 2. <PERSON><PERSON><PERSON><PERSON> sản phẩm (categoryId)
- **Trường**: `categoryId`
- **Validation**: Bắ<PERSON> buộc chọn
- **Thông báo lỗi**: "Nhóm sản phẩm là trường bắt buộc"
- **Vị trí**: Dòng 353-358 trong FormProducts/index.tsx

### 3. <PERSON><PERSON><PERSON> cung cấp (supplierId)
- **Trường**: `supplierId`
- **Validation**: <PERSON><PERSON><PERSON> buộc chọn
- **Thông báo lỗi**: "Nhà cung cấp là trường bắt buộc"
- **Vị trí**: Dòng 367-372 trong FormProducts/index.tsx

## Cách hoạt động:

1. **Validation Function**: `validateRequiredFields()` kiểm tra các trường bắt buộc
2. **Submit Handler**: `handleFormSubmit()` gọi validation trước khi submit
3. **Error Display**: Sử dụng `methods.setError()` để hiển thị lỗi trên từng field
4. **Toast Message**: Hiển thị thông báo lỗi chung khi có validation error

## Test Cases:

### Test 1: Submit form trống
- **Kết quả mong đợi**: Hiển thị 3 lỗi validation và toast error
- **Các lỗi**: 
  - "Tên sản phẩm là trường bắt buộc"
  - "Nhóm sản phẩm là trường bắt buộc" 
  - "Nhà cung cấp là trường bắt buộc"

### Test 2: Submit với chỉ tên sản phẩm
- **Kết quả mong đợi**: Hiển thị 2 lỗi validation
- **Các lỗi**:
  - "Nhóm sản phẩm là trường bắt buộc"
  - "Nhà cung cấp là trường bắt buộc"

### Test 3: Submit với đầy đủ thông tin bắt buộc
- **Kết quả mong đợi**: Form submit thành công, không có lỗi validation

## Cách kiểm tra:

1. Mở form tạo mới sản phẩm
2. Bấm nút "Tạo mới" mà không điền gì
3. Kiểm tra xem có hiển thị lỗi validation không
4. Điền từng trường và kiểm tra lỗi có biến mất không
